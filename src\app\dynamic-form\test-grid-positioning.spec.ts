import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { DynamicFormComponent } from './dynamic-form.component';

describe('DynamicFormComponent - Grid Positioning', () => {
  let component: DynamicFormComponent;
  let fixture: any;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DynamicFormComponent],
      imports: [],
      providers: [FormBuilder]
    }).compileComponents();

    fixture = TestBed.createComponent(DynamicFormComponent);
    component = fixture.componentInstance;
  });

  describe('Grid Detection Logic', () => {
    it('should detect fields with positioning data', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'field2', row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).hasGridPositioning(fieldsWithPositioning);
      expect(result).toBe(true);
    });

    it('should not detect fields without positioning data', () => {
      const fieldsWithoutPositioning = [
        { fieldName: 'field1', type: 'string' },
        { fieldName: 'field2', type: 'string' }
      ];

      const result = (component as any).hasGridPositioning(fieldsWithoutPositioning);
      expect(result).toBe(false);
    });

    it('should use grid layout when columnNumber > 1 and positioning exists', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).shouldUseGridLayout(fieldsWithPositioning, 2);
      expect(result).toBe(true);
    });

    it('should not use grid layout when columnNumber = 1', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).shouldUseGridLayout(fieldsWithPositioning, 1);
      expect(result).toBe(false);
    });
  });

  describe('Grid Area Calculation', () => {
    it('should calculate correct grid area for simple field', () => {
      const field = { row: 1, column: 1, rowSize: 1, colSize: 1 };
      const result = (component as any).calculateGridArea(field);
      expect(result).toBe('1 / 1 / 2 / 2');
    });

    it('should calculate correct grid area for spanning field', () => {
      const field = { row: 2, column: 1, rowSize: 2, colSize: 3 };
      const result = (component as any).calculateGridArea(field);
      expect(result).toBe('2 / 1 / 4 / 4');
    });

    it('should handle missing properties with defaults', () => {
      const field = { row: 1, column: 1 };
      const result = (component as any).calculateGridArea(field);
      expect(result).toBe('1 / 1 / 2 / 2');
    });
  });

  describe('Position Conflict Resolution', () => {
    it('should resolve conflicts by response order priority', () => {
      const conflictingFields = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'field2', row: 1, column: 1, rowSize: 1, colSize: 1 } // Same position
      ];

      const result = (component as any).resolvePositionConflicts(conflictingFields);
      
      // First field should keep position, second should lose it
      expect(result[0].gridArea).toBe('1 / 1 / 2 / 2');
      expect(result[1].row).toBeUndefined();
      expect(result[1].column).toBeUndefined();
    });

    it('should handle overlapping spans correctly', () => {
      const overlappingFields = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 2, colSize: 2 }, // Covers 1,1 to 2,2
        { fieldName: 'field2', row: 2, column: 2, rowSize: 1, colSize: 1 }  // Overlaps at 2,2
      ];

      const result = (component as any).resolvePositionConflicts(overlappingFields);
      
      // First field should keep position, second should lose it
      expect(result[0].gridArea).toBe('1 / 1 / 3 / 3');
      expect(result[1].row).toBeUndefined();
    });
  });

  describe('Field Distribution', () => {
    it('should use grid distribution when positioning data exists', () => {
      const fieldsWithPositioning = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'field2', row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(fieldsWithPositioning, 2);
      
      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(2);
      expect(result.fields[0].gridArea).toBeDefined();
    });

    it('should use round-robin distribution when no positioning data', () => {
      const fieldsWithoutPositioning = [
        { fieldName: 'field1', type: 'string' },
        { fieldName: 'field2', type: 'string' }
      ];

      const result = (component as any).distributeFields(fieldsWithoutPositioning, 2);
      
      expect(result.useGrid).toBe(false);
      expect(result.columns.length).toBe(2);
      expect(result.columns[0].length).toBe(1);
      expect(result.columns[1].length).toBe(1);
    });
  });

  describe('Integration Test', () => {
    it('should handle the provided metadata example correctly', () => {
      const testMetadata = {
        columnNumber: 2,
        fieldName: [
          { fieldName: "nickName", type: "string", row: 1, column: 1, rowSize: 1, colSize: 1 },
          { fieldName: "fullName", type: "string", mandatory: true, row: 1, column: 2, rowSize: 1, colSize: 1 },
          { fieldName: "gender", type: "string", mandatory: true, row: 2, column: 1, rowSize: 1, colSize: 1 },
          { fieldName: "mobileNo", type: "string", isMulti: true, mandatory: true, row: 2, column: 2, rowSize: 1, colSize: 1 }
        ]
      };

      // Test field distribution
      const visibleFields = testMetadata.fieldName;
      const result = (component as any).distributeFields(visibleFields, testMetadata.columnNumber);

      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(4);

      // Check specific positioning
      const nickNameField = result.fields.find((f: any) => f.fieldName === 'nickName');
      const fullNameField = result.fields.find((f: any) => f.fieldName === 'fullName');

      expect(nickNameField.gridArea).toBe('1 / 1 / 2 / 2');
      expect(fullNameField.gridArea).toBe('1 / 2 / 2 / 3');
    });
  });

  describe('Group Priority Sorting', () => {
    it('should prioritize group fields with positioning data', () => {
      const mixedFields = [
        { fieldName: 'regularField1', row: 2, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField1', Group: 'testGroup', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'regularField2', type: 'string' }, // No positioning
        { fieldName: 'groupField2', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const sortedFields = (component as any).sortFieldsForGroupPriority(mixedFields);

      // Group fields with positioning should come first
      expect(sortedFields[0].fieldName).toBe('groupField1');
      expect(sortedFields[1].fieldName).toBe('groupField2');
      expect(sortedFields[2].fieldName).toBe('regularField1');
      expect(sortedFields[3].fieldName).toBe('regularField2');
    });

    it('should sort group fields by position within groups', () => {
      const groupFields = [
        { fieldName: 'groupField3', Group: 'testGroup', row: 2, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField1', Group: 'testGroup', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField2', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 }
      ];

      const sortedFields = (component as any).sortFieldsByPosition(groupFields);

      // Should be sorted by row, then column
      expect(sortedFields[0].fieldName).toBe('groupField1'); // row 1, col 1
      expect(sortedFields[1].fieldName).toBe('groupField2'); // row 1, col 2
      expect(sortedFields[2].fieldName).toBe('groupField3'); // row 2, col 2
    });
  });
});
