// Manual test for group priority functionality
// This file can be run in browser console to test the group priority logic

export class GroupPriorityTestRunner {
  
  // Simulate the group priority methods from DynamicFormComponent
  private sortFieldsForGroupPriority(fields: any[]): any[] {
    return fields.sort((a, b) => {
      const aIsGroup = !!a.Group;
      const bIsGroup = !!b.Group;
      const aHasPosition = !!(a.row && a.column);
      const bHasPosition = !!(b.row && b.column);

      // Priority 1: Group fields with positioning data
      if (aIsGroup && aHasPosition && (!bIsGroup || !bHasPosition)) {
        return -1;
      }
      if (bIsGroup && bHasPosition && (!aIsGroup || !aHasPosition)) {
        return 1;
      }

      // Priority 2: Both are group fields with positioning - sort by position
      if (aIsGroup && bIsGroup && aHasPosition && bHasPosition) {
        if (a.row !== b.row) {
          return a.row - b.row;
        }
        return a.column - b.column;
      }

      // Priority 3: Non-group fields with positioning
      if (!aIsGroup && aHasPosition && bIsGroup && !bHasPosition) {
        return -1;
      }
      if (!bIsGroup && bHasPosition && aIsGroup && !aHasPosition) {
        return 1;
      }

      // Priority 4: Both non-group fields with positioning - sort by position
      if (!aIsGroup && !bIsGroup && aHasPosition && bHasPosition) {
        if (a.row !== b.row) {
          return a.row - b.row;
        }
        return a.column - b.column;
      }

      // Priority 5: Fields without positioning maintain original order
      return 0;
    });
  }

  private sortFieldsByPosition(fields: any[]): any[] {
    return fields.sort((a, b) => {
      const aHasPosition = !!(a.row && a.column);
      const bHasPosition = !!(b.row && b.column);

      // Fields with positioning come first
      if (aHasPosition && !bHasPosition) return -1;
      if (!aHasPosition && bHasPosition) return 1;

      // Both have positioning - sort by row, then column
      if (aHasPosition && bHasPosition) {
        if (a.row !== b.row) {
          return a.row - b.row;
        }
        return a.column - b.column;
      }

      // Neither has positioning - maintain original order
      return 0;
    });
  }

  // Test cases
  runTests() {
    console.log('🧪 Running Group Priority Tests...\n');
    
    this.testGroupPrioritySorting();
    this.testPositionSorting();
    this.testMixedScenario();
    
    console.log('✅ All tests completed!');
  }

  testGroupPrioritySorting() {
    console.log('📋 Test 1: Group Priority Sorting');
    
    const mixedFields = [
      { fieldName: 'regularField1', row: 2, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'groupField1', Group: 'testGroup', row: 1, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'regularField2', type: 'string' }, // No positioning
      { fieldName: 'groupField2', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 }
    ];

    console.log('📋 Input (mixed fields):', mixedFields.map(f => f.fieldName));

    const sorted = this.sortFieldsForGroupPriority(mixedFields);
    console.log('✅ Sorted fields:', sorted.map(f => f.fieldName));

    // Expected order: groupField1, groupField2, regularField1, regularField2
    const expectedOrder = ['groupField1', 'groupField2', 'regularField1', 'regularField2'];
    const actualOrder = sorted.map(f => f.fieldName);
    
    console.log('📍 Expected:', expectedOrder);
    console.log('📍 Actual:', actualOrder);
    console.log('📍 Match:', JSON.stringify(expectedOrder) === JSON.stringify(actualOrder) ? '✅' : '❌');
    console.log('');
  }

  testPositionSorting() {
    console.log('📋 Test 2: Position Sorting within Groups');
    
    const groupFields = [
      { fieldName: 'groupField3', Group: 'testGroup', row: 2, column: 2, rowSize: 1, colSize: 1 },
      { fieldName: 'groupField1', Group: 'testGroup', row: 1, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'groupField2', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 },
      { fieldName: 'groupField4', Group: 'testGroup' } // No positioning
    ];

    console.log('📋 Input (group fields):', groupFields.map(f => `${f.fieldName} (${f.row || 'no'},${f.column || 'no'})`));

    const sorted = this.sortFieldsByPosition(groupFields);
    console.log('✅ Sorted fields:', sorted.map(f => `${f.fieldName} (${f.row || 'no'},${f.column || 'no'})`));

    // Expected order: groupField1 (1,1), groupField2 (1,2), groupField3 (2,2), groupField4 (no positioning)
    const expectedOrder = ['groupField1', 'groupField2', 'groupField3', 'groupField4'];
    const actualOrder = sorted.map(f => f.fieldName);
    
    console.log('📍 Expected:', expectedOrder);
    console.log('📍 Actual:', actualOrder);
    console.log('📍 Match:', JSON.stringify(expectedOrder) === JSON.stringify(actualOrder) ? '✅' : '❌');
    console.log('');
  }

  testMixedScenario() {
    console.log('📋 Test 3: Mixed Scenario (Real-world example)');
    
    const realWorldFields = [
      { fieldName: 'name', type: 'string', row: 3, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'address1', Group: 'address', row: 1, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'email', type: 'string' }, // No positioning
      { fieldName: 'address2', Group: 'address', row: 1, column: 2, rowSize: 1, colSize: 1 },
      { fieldName: 'phone1', Group: 'contact', row: 2, column: 1, rowSize: 1, colSize: 1 },
      { fieldName: 'age', type: 'number', row: 3, column: 2, rowSize: 1, colSize: 1 },
      { fieldName: 'phone2', Group: 'contact', row: 2, column: 2, rowSize: 1, colSize: 1 }
    ];

    console.log('📋 Input (real-world fields):');
    realWorldFields.forEach(f => {
      const pos = f.row && f.column ? `(${f.row},${f.column})` : '(no pos)';
      const group = f.Group ? `[${f.Group}]` : '[no group]';
      console.log(`  - ${f.fieldName} ${group} ${pos}`);
    });

    const sorted = this.sortFieldsForGroupPriority(realWorldFields);
    console.log('✅ Sorted fields:');
    sorted.forEach((f, i) => {
      const pos = f.row && f.column ? `(${f.row},${f.column})` : '(no pos)';
      const group = f.Group ? `[${f.Group}]` : '[no group]';
      console.log(`  ${i + 1}. ${f.fieldName} ${group} ${pos}`);
    });

    // Expected order: address1, address2, phone1, phone2, name, age, email
    const expectedOrder = ['address1', 'address2', 'phone1', 'phone2', 'name', 'age', 'email'];
    const actualOrder = sorted.map(f => f.fieldName);
    
    console.log('📍 Expected:', expectedOrder);
    console.log('📍 Actual:', actualOrder);
    console.log('📍 Match:', JSON.stringify(expectedOrder) === JSON.stringify(actualOrder) ? '✅' : '❌');
    console.log('');
  }
}

// Usage: 
// const tester = new GroupPriorityTestRunner();
// tester.runTests();
